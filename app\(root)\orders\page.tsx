"use client";
import React, { useState, useMemo } from "react";
import { useOrders } from "@/contexts/OrderContext";
import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Package, Loader2, Grid, List, LayoutGrid } from "lucide-react";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import OrderCard from "@/components/orders/OrderCard";
import OrderTable from "@/components/orders/OrderTable";
import OrderFilters, { OrderFilters as FilterType } from "@/components/orders/OrderFilters";
import OrderStats from "@/components/orders/OrderStats";
import Pagination from "@/components/ui/pagination";
import LoadingState from "@/components/ui/loading-state";
import EmptyState from "@/components/ui/empty-state";
import { Order } from "@/types/order";

const ITEMS_PER_PAGE = 10;

const OrdersPage = () => {
  const { orders, isLoading } = useOrders();
  const [filters, setFilters] = useState<FilterType>({
    search: "",
    status: "all",
    dateFrom: "",
    dateTo: "",
    minAmount: "",
    maxAmount: "",
    sortBy: "date",
    sortOrder: "desc"
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<"cards" | "table">("cards");

  // Filter and sort orders
  const filteredAndSortedOrders = useMemo(() => {
    let filtered = [...orders];

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(order =>
        order.id.toLowerCase().includes(searchLower) ||
        `${order.customer.firstName} ${order.customer.lastName}`.toLowerCase().includes(searchLower) ||
        order.customer.email.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (filters.status !== "all") {
      filtered = filtered.filter(order => order.status === filters.status);
    }

    // Apply date filters
    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom);
      filtered = filtered.filter(order => new Date(order.createdAt) >= fromDate);
    }
    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo);
      toDate.setHours(23, 59, 59, 999); // End of day
      filtered = filtered.filter(order => new Date(order.createdAt) <= toDate);
    }

    // Apply amount filters
    if (filters.minAmount) {
      filtered = filtered.filter(order => order.total >= parseFloat(filters.minAmount));
    }
    if (filters.maxAmount) {
      filtered = filtered.filter(order => order.total <= parseFloat(filters.maxAmount));
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (filters.sortBy) {
        case "date":
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
        case "amount":
          aValue = a.total;
          bValue = b.total;
          break;
        case "status":
          aValue = a.status;
          bValue = b.status;
          break;
        default:
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
      }

      if (filters.sortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [orders, filters]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedOrders.length / ITEMS_PER_PAGE);
  const paginatedOrders = filteredAndSortedOrders.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  // Reset page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  const handleFiltersChange = (newFilters: FilterType) => {
    setFilters(newFilters);
  };

  const handleSort = (field: string) => {
    setFilters(prev => ({
      ...prev,
      sortBy: field as any,
      sortOrder: prev.sortBy === field && prev.sortOrder === "desc" ? "asc" : "desc"
    }));
  };

  // Loading state
  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
            <div>
              <div className="h-8 bg-gray-200 rounded w-48 mb-2 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-64 animate-pulse"></div>
            </div>
          </div>

          <LoadingState
            type="spinner"
            size="lg"
            text="Loading your orders..."
            className="py-20"
          />
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="flex flex-col gap-4 mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">My Orders</h1>
              <p className="text-gray-600 mt-1 text-sm sm:text-base">
                Track and manage your order history
              </p>
            </div>

            {orders.length > 0 && (
              <div className="flex items-center gap-3 w-full sm:w-auto">
                <span className="text-sm text-gray-500 hidden sm:inline">View:</span>
                <div className="flex rounded-lg border w-full sm:w-auto">
                  <Button
                    variant={viewMode === "cards" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("cards")}
                    className="rounded-r-none flex-1 sm:flex-none min-h-[44px] gap-2"
                  >
                    <LayoutGrid size={16} />
                    <span className="sm:hidden">Cards</span>
                  </Button>
                  <Button
                    variant={viewMode === "table" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("table")}
                    className="rounded-l-none flex-1 sm:flex-none min-h-[44px] gap-2"
                  >
                    <List size={16} />
                    <span className="sm:hidden">Table</span>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {orders.length === 0 ? (
          <EmptyState
            icon={Package}
            title="No orders yet"
            description="You haven't placed any orders yet. Start shopping to see your orders here."
            action={{
              label: "Start Shopping",
              onClick: () => window.location.href = "/products"
            }}
          />
        ) : (
          <>
            {/* Order Statistics */}
            <OrderStats orders={orders} className="mb-8" />

            {/* Filters */}
            <OrderFilters
              onFiltersChange={handleFiltersChange}
              totalOrders={orders.length}
              filteredCount={filteredAndSortedOrders.length}
            />

            {/* Orders Display */}
            {filteredAndSortedOrders.length === 0 ? (
              <EmptyState
                icon={Package}
                title="No orders found"
                description="Try adjusting your filters to see more results."
                className="py-12"
              />
            ) : (
              <>
                {viewMode === "cards" ? (
                  <div className="space-y-4 mb-8">
                    {paginatedOrders.map((order, index) => (
                      <OrderCard key={order.id} order={order} index={index} />
                    ))}
                  </div>
                ) : (
                  <div className="mb-8">
                    <OrderTable
                      orders={paginatedOrders}
                      onSort={handleSort}
                      sortField={filters.sortBy}
                      sortOrder={filters.sortOrder}
                    />
                  </div>
                )}

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center">
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={setCurrentPage}
                    />
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>
    </ProtectedRoute>
  );
};

export default OrdersPage;
