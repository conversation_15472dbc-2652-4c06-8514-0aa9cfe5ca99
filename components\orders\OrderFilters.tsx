import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { OrderStatus } from "@/types/order";
import { 
  Search, 
  Filter, 
  X, 
  Calendar,
  SlidersHorizontal 
} from "lucide-react";

interface OrderFiltersProps {
  onFiltersChange: (filters: OrderFilters) => void;
  totalOrders: number;
  filteredCount: number;
}

export interface OrderFilters {
  search: string;
  status: OrderStatus | "all";
  dateFrom: string;
  dateTo: string;
  minAmount: string;
  maxAmount: string;
  sortBy: "date" | "amount" | "status";
  sortOrder: "asc" | "desc";
}

const defaultFilters: OrderFilters = {
  search: "",
  status: "all",
  dateFrom: "",
  dateTo: "",
  minAmount: "",
  maxAmount: "",
  sortBy: "date",
  sortOrder: "desc"
};

const OrderFilters: React.FC<OrderFiltersProps> = ({
  onFiltersChange,
  totalOrders,
  filteredCount
}) => {
  const [filters, setFilters] = useState<OrderFilters>(defaultFilters);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleFilterChange = (key: keyof OrderFilters, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    setFilters(defaultFilters);
    onFiltersChange(defaultFilters);
    setShowAdvanced(false);
  };

  const hasActiveFilters = Object.entries(filters).some(([key, value]) => {
    if (key === "sortBy" && value === "date") return false;
    if (key === "sortOrder" && value === "desc") return false;
    return value !== "" && value !== "all";
  });

  return (
    <Card className="mb-6">
      <CardHeader className="pb-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter size={20} />
            Filter Orders
          </CardTitle>
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm text-muted-foreground">
            <span className="text-center sm:text-left">
              Showing {filteredCount} of {totalOrders} orders
            </span>
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-xs min-h-[36px] w-full sm:w-auto"
              >
                <X size={14} className="mr-1" />
                Clear Filters
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Search and Status Row */}
        <div className="grid grid-cols-1 gap-4">
          <div>
            <Label htmlFor="search" className="text-sm font-medium">Search Orders</Label>
            <div className="relative mt-1">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Search by order ID, customer name, or email..."
                value={filters.search}
                onChange={(e) => handleFilterChange("search", e.target.value)}
                className="pl-10 min-h-[44px]"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="status" className="text-sm font-medium">Status</Label>
            <Select
              value={filters.status}
              onValueChange={(value) => handleFilterChange("status", value)}
            >
              <SelectTrigger className="min-h-[44px] mt-1">
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="shipped">Shipped</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Advanced Filters Toggle and Sort */}
        <div className="flex flex-col gap-4 pt-2 border-t">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center gap-2 justify-center sm:justify-start min-h-[44px] w-full sm:w-auto"
          >
            <SlidersHorizontal size={16} />
            {showAdvanced ? "Hide" : "Show"} Advanced Filters
          </Button>

          <div className="flex flex-col sm:flex-row sm:items-center gap-3">
            <Label htmlFor="sortBy" className="text-sm font-medium sm:whitespace-nowrap">Sort by:</Label>
            <div className="flex gap-2 flex-1">
              <Select
                value={filters.sortBy}
                onValueChange={(value) => handleFilterChange("sortBy", value)}
              >
                <SelectTrigger className="flex-1 sm:w-32 min-h-[44px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Date</SelectItem>
                  <SelectItem value="amount">Amount</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filters.sortOrder}
                onValueChange={(value) => handleFilterChange("sortOrder", value)}
              >
                <SelectTrigger className="w-20 sm:w-24 min-h-[44px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">↓</SelectItem>
                  <SelectItem value="asc">↑</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4 border-t">
            <div>
              <Label htmlFor="dateFrom" className="text-sm font-medium">From Date</Label>
              <div className="relative mt-1">
                <Calendar size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                <Input
                  id="dateFrom"
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange("dateFrom", e.target.value)}
                  className="pl-10 min-h-[44px]"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="dateTo" className="text-sm font-medium">To Date</Label>
              <div className="relative mt-1">
                <Calendar size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                <Input
                  id="dateTo"
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange("dateTo", e.target.value)}
                  className="pl-10 min-h-[44px]"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="minAmount" className="text-sm font-medium">Min Amount</Label>
              <Input
                id="minAmount"
                type="number"
                placeholder="0"
                value={filters.minAmount}
                onChange={(e) => handleFilterChange("minAmount", e.target.value)}
                className="min-h-[44px] mt-1"
              />
            </div>

            <div>
              <Label htmlFor="maxAmount" className="text-sm font-medium">Max Amount</Label>
              <Input
                id="maxAmount"
                type="number"
                placeholder="No limit"
                value={filters.maxAmount}
                onChange={(e) => handleFilterChange("maxAmount", e.target.value)}
                className="min-h-[44px] mt-1"
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default OrderFilters;
