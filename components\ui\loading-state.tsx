import React from "react";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";

interface LoadingStateProps {
  type?: "spinner" | "skeleton" | "pulse";
  size?: "sm" | "md" | "lg";
  text?: string;
  className?: string;
}

const LoadingState: React.FC<LoadingStateProps> = ({
  type = "spinner",
  size = "md",
  text,
  className = ""
}) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12"
  };

  const textSizeClasses = {
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg"
  };

  if (type === "spinner") {
    return (
      <div className={`flex flex-col items-center justify-center gap-3 ${className}`}>
        <Loader2 className={`${sizeClasses[size]} animate-spin text-accent`} />
        {text && (
          <p className={`${textSizeClasses[size]} text-muted-foreground`}>
            {text}
          </p>
        )}
      </div>
    );
  }

  if (type === "skeleton") {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        </div>
      </div>
    );
  }

  if (type === "pulse") {
    return (
      <motion.div
        className={`bg-gray-100 rounded-lg ${className}`}
        animate={{
          opacity: [0.5, 1, 0.5],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    );
  }

  return null;
};

export default LoadingState;
