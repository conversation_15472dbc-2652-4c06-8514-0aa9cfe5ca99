import React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { Order } from "@/types/order";
import { useCurrency } from "@/contexts/CurrencyContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import OrderStatusBadge from "./OrderStatusBadge";
import { 
  ShoppingBag, 
  Calendar, 
  Package, 
  Eye,
  MoreHorizontal 
} from "lucide-react";

interface OrderCardProps {
  order: Order;
  index?: number;
  onViewDetails?: (orderId: string) => void;
}

const OrderCard: React.FC<OrderCardProps> = ({ 
  order, 
  index = 0, 
  onViewDetails 
}) => {
  const { formatPrice } = useCurrency();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    }).format(date);
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(order.id);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      whileHover={{ y: -2 }}
      className="group"
    >
      <Card
        className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-accent/20 hover:border-l-accent focus-within:ring-2 focus-within:ring-accent focus-within:ring-offset-2"
        role="article"
        aria-label={`Order ${order.id.slice(-8).toUpperCase()}`}
      >
        <CardContent className="p-4 sm:p-6">
          <div className="flex flex-col gap-4">
            {/* Order Header */}
            <div className="flex items-start justify-between gap-3">
              <div className="space-y-1 min-w-0 flex-1">
                <div className="flex items-center gap-2">
                  <ShoppingBag size={16} className="text-accent flex-shrink-0" aria-hidden="true" />
                  <Link
                    href={`/orders/${order.id}`}
                    className="font-semibold text-base sm:text-lg text-accent hover:underline focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 rounded truncate"
                    aria-label={`View details for order ${order.id.slice(-8).toUpperCase()}`}
                  >
                    #{order.id.slice(-8).toUpperCase()}
                  </Link>
                </div>
                <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                  <Calendar size={12} className="flex-shrink-0" aria-hidden="true" />
                  <span className="truncate">Placed on {formatDate(order.createdAt)}</span>
                </div>
              </div>
              <OrderStatusBadge status={order.status} size="md" />
            </div>

            {/* Order Summary */}
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center gap-1">
                <Package size={14} className="text-muted-foreground flex-shrink-0" />
                <span className="text-sm text-muted-foreground">
                  {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                </span>
              </div>
              <div className="font-semibold text-lg sm:text-xl text-accent">
                {formatPrice(order.total)}
              </div>
            </div>

            {/* Customer Info */}
            <div className="text-xs sm:text-sm text-muted-foreground truncate">
              <span>{order.customer.firstName} {order.customer.lastName}</span>
              {order.shipping.city && (
                <span className="ml-2">• {order.shipping.city}</span>
              )}
            </div>

            {/* Actions */}
            <div className="flex gap-2 pt-2 border-t">
              <Link href={`/orders/${order.id}`} className="flex-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full group-hover:border-accent group-hover:text-accent transition-colors min-h-[44px]"
                  onClick={handleViewDetails}
                >
                  <Eye size={14} className="mr-2" />
                  View Details
                </Button>
              </Link>

              <Button
                variant="ghost"
                size="sm"
                className="sm:hidden min-h-[44px] px-3"
                aria-label="More options"
              >
                <MoreHorizontal size={16} />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default OrderCard;
