import React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { Order } from "@/types/order";
import { useCurrency } from "@/contexts/CurrencyContext";
import { Button } from "@/components/ui/button";
import OrderStatusBadge from "./OrderStatusBadge";
import { 
  Eye, 
  Package, 
  User,
  Calendar,
  ArrowUpDown
} from "lucide-react";

interface OrderTableProps {
  orders: Order[];
  onSort?: (field: string) => void;
  sortField?: string;
  sortOrder?: "asc" | "desc";
  loading?: boolean;
}

const OrderTable: React.FC<OrderTableProps> = ({
  orders,
  onSort,
  sortField,
  sortOrder,
  loading = false
}) => {
  const { formatPrice } = useCurrency();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    }).format(date);
  };

  const handleSort = (field: string) => {
    if (onSort) {
      onSort(field);
    }
  };

  const SortButton: React.FC<{ field: string; children: React.ReactNode }> = ({ 
    field, 
    children 
  }) => (
    <button
      onClick={() => handleSort(field)}
      className="flex items-center gap-1 hover:text-accent transition-colors"
      disabled={!onSort}
    >
      {children}
      {onSort && (
        <ArrowUpDown 
          size={14} 
          className={`transition-colors ${
            sortField === field ? 'text-accent' : 'text-muted-foreground'
          }`}
        />
      )}
    </button>
  );

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="animate-pulse">
          <div className="h-12 bg-gray-100 border-b"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-50 border-b"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      {/* Desktop Table */}
      <div className="hidden lg:block overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 border-b">
            <tr>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                <SortButton field="id">Order ID</SortButton>
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                <SortButton field="customer">Customer</SortButton>
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                <SortButton field="date">Date</SortButton>
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                <SortButton field="items">Items</SortButton>
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                <SortButton field="total">Total</SortButton>
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                <SortButton field="status">Status</SortButton>
              </th>
              <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {orders.map((order, index) => (
              <motion.tr
                key={order.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="hover:bg-gray-50 transition-colors"
              >
                <td className="px-6 py-4">
                  <Link href={`/orders/${order.id}`}>
                    <span className="font-medium text-accent hover:underline cursor-pointer">
                      #{order.id.slice(-8).toUpperCase()}
                    </span>
                  </Link>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center gap-2">
                    <User size={16} className="text-muted-foreground" />
                    <div>
                      <div className="font-medium text-sm">
                        {order.customer.firstName} {order.customer.lastName}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {order.customer.email}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar size={14} className="text-muted-foreground" />
                    {formatDate(order.createdAt)}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center gap-2 text-sm">
                    <Package size={14} className="text-muted-foreground" />
                    {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <span className="font-semibold">
                    {formatPrice(order.total)}
                  </span>
                </td>
                <td className="px-6 py-4">
                  <OrderStatusBadge status={order.status} size="sm" />
                </td>
                <td className="px-6 py-4 text-right">
                  <Link href={`/orders/${order.id}`}>
                    <Button variant="outline" size="sm">
                      <Eye size={14} className="mr-1" />
                      View
                    </Button>
                  </Link>
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Cards */}
      <div className="lg:hidden divide-y divide-gray-100">
        {orders.map((order, index) => (
          <motion.div
            key={order.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
            className="p-4 hover:bg-gray-50 transition-colors"
          >
            <div className="flex justify-between items-start mb-3">
              <div>
                <Link href={`/orders/${order.id}`}>
                  <span className="font-semibold text-accent hover:underline">
                    #{order.id.slice(-8).toUpperCase()}
                  </span>
                </Link>
                <div className="text-sm text-muted-foreground mt-1">
                  {formatDate(order.createdAt)}
                </div>
              </div>
              <OrderStatusBadge status={order.status} size="sm" />
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Customer:</span>
                <span>{order.customer.firstName} {order.customer.lastName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Items:</span>
                <span>{order.items.length}</span>
              </div>
              <div className="flex justify-between font-semibold">
                <span>Total:</span>
                <span>{formatPrice(order.total)}</span>
              </div>
            </div>
            
            <div className="mt-3 pt-3 border-t">
              <Link href={`/orders/${order.id}`}>
                <Button variant="outline" size="sm" className="w-full">
                  <Eye size={14} className="mr-2" />
                  View Details
                </Button>
              </Link>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default OrderTable;
