import React from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { LucideIcon } from "lucide-react";

interface EmptyStateProps {
  icon?: LucideIcon;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: "default" | "outline" | "ghost";
  };
  className?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon: Icon,
  title,
  description,
  action,
  className = ""
}) => {
  return (
    <motion.div
      className={`flex flex-col items-center justify-center py-16 text-center ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {Icon && (
        <div className="bg-gray-100 rounded-full p-6 mb-6">
          <Icon size={48} className="text-gray-400" />
        </div>
      )}
      
      <h2 className="text-2xl font-semibold mb-2 text-gray-900">
        {title}
      </h2>
      
      {description && (
        <p className="text-gray-600 mb-8 max-w-md">
          {description}
        </p>
      )}
      
      {action && (
        <Button
          variant={action.variant || "default"}
          size="lg"
          onClick={action.onClick}
        >
          {action.label}
        </Button>
      )}
    </motion.div>
  );
};

export default EmptyState;
